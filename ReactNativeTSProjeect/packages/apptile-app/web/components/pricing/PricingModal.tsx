import React, {useEffect, useState} from 'react';
import {View, StyleSheet, Pressable, Modal, ActivityIndicator} from 'react-native';
import {useSelector} from 'react-redux';
import {MaterialCommunityIcons} from 'apptile-core';
import TextElement from '../../components-v2/base/TextElement';
import Button from '../../components-v2/base/Button';
import {PaddlePlan, getPaddleService} from '../../services/PaddleService';
import {PaddleProduct} from '../../api/ApiTypes';
import {EditorRootState} from '../../store/EditorRootState';
import theme from '../../styles-v2/theme';

interface PricingModalProps {
  visible: boolean;
  onClose: () => void;
}

const PricingModal: React.FC<PricingModalProps> = ({visible, onClose}) => {
  const [products, setProducts] = useState<PaddleProduct[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [selectedPlan, setSelectedPlan] = useState<PaddlePlan | null>(null);
  const [selectedProductId, setSelectedProductId] = useState<string | null>(null);
  const [dropdownOpen, setDropdownOpen] = useState(false);

  const {user} = useSelector((state: EditorRootState) => state.user);
  const {orgId} = useSelector((state: EditorRootState) => state.apptile);

  useEffect(() => {
    if (visible) {
      loadProducts();
    } else {
      // Close dropdown when modal closes
      setDropdownOpen(false);
    }
  }, [visible]);

  const loadProducts = async () => {
    try {
      setLoading(true);
      setError(null);
      const paddleService = getPaddleService('test_f0ff365cf3b22228fc6dde90827');
      await paddleService.initialize();
      const fetchedProducts = await paddleService.fetchProductsWithPlans();
      setProducts(fetchedProducts);

      // Auto-select the first product if available and set default plan
      if (fetchedProducts.length > 0) {
        const firstProduct = fetchedProducts[0];
        setSelectedProductId(firstProduct.id);

        // Set the first monthly plan as default
        const monthlyPlans = firstProduct.plans.filter(p => p.billing_cycle.interval === 'month');
        if (monthlyPlans.length > 0) {
          setSelectedPlan(monthlyPlans[0]);
        }
      }
    } catch (err) {
      console.error('Failed to load products:', err);
      setError('Failed to load pricing plans. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  const handleUpgrade = async (plan: PaddlePlan) => {
    try {
      const paddleService = getPaddleService();
      await paddleService.openCheckout({
        items: [
          {
            priceId: plan.id,
            quantity: 1,
          },
        ],
        customer: {
          email: user?.email,
        },
        customData: {
          organizationId: orgId?.toString(),
          userId: user?.id?.toString(),
          credits: plan.custom_data?.credits,
        },
      });
    } catch (err) {
      console.error('Failed to open checkout:', err);
    }
  };

  // Render Free Plan Card (hardcoded)
  const renderFreePlanCard = () => (
    <View style={[styles.planCard, styles.freePlanCard]}>
      <TextElement fontSize="xl" fontWeight="600" style={[styles.planTitle, {color: '#FFFFFF'}]}>
        Free
      </TextElement>

      <View style={styles.priceSection}>
        <View style={styles.priceContainer}>
          <TextElement fontSize="4xl" fontWeight="700" style={{color: '#FFFFFF'}}>
            $0
          </TextElement>
          <TextElement fontSize="lg" style={{color: '#BCBCBC'}}>
            /month
          </TextElement>
        </View>
      </View>

      <TextElement fontSize="sm" fontWeight="600" style={[styles.featuresTitle, {color: '#FFFFFF'}]}>
        Get started with:
      </TextElement>

      <View style={styles.featuresList}>
        {[
          'Unlimited projects',
          'Visual Editing',
          'Chat support via Discord',
          'Limited access to Advanced Coding Model',
        ].map((feature, index) => (
          <View key={index} style={styles.featureItem}>
            <MaterialCommunityIcons name="check" size={16} color={theme.SUCCESS} style={styles.checkIcon} />
            <TextElement fontSize="sm" style={[styles.featureText, {color: '#BCBCBC'}]}>
              {feature}
            </TextElement>
          </View>
        ))}
      </View>

      <View style={styles.creditsContainer}>
        <TextElement fontSize="sm" fontWeight="600" style={[styles.creditsText, {color: '#BCBCBC'}]}>
          1000 monthly credits
        </TextElement>
      </View>

      <TextElement fontSize="sm" style={[styles.planSubtitle, {color: '#BCBCBC'}]}>
        For getting started
      </TextElement>

      <View style={styles.buttonContainer}>
        <Button
          color="SECONDARY"
          size="MEDIUM"
          onPress={() => {
            /* Handle free plan selection */
          }}
          containerStyles={styles.selectButton}>
          Get started
        </Button>
      </View>
    </View>
  );

  // Render Pro Plan Card with dropdown
  const renderProPlanCard = () => {
    const selectedProduct = products.find(p => p.id === selectedProductId);
    if (!selectedProduct) return null;

    const monthlyPlans = selectedProduct.plans.filter(p => p.billing_cycle.interval === 'month');
    const currentSelectedPlan =
      selectedPlan && selectedPlan.product_id === selectedProductId ? selectedPlan : monthlyPlans[0];

    const handlePlanSelect = (plan: PaddlePlan) => {
      setSelectedPlan(plan);
      setDropdownOpen(false);
    };

    return (
      <View style={[styles.planCard, styles.proPlanCard]}>
        {/* Popular Badge */}
        <View style={styles.popularBadge}>
          <TextElement fontSize="xs" fontWeight="600" style={{color: theme.DEFAULT_COLOR}}>
            POPULAR
          </TextElement>
        </View>

        <TextElement fontSize="xl" fontWeight="600" style={[styles.planTitle, {marginTop: 20, color: '#FFFFFF'}]}>
          {selectedProduct.name}
        </TextElement>

        <View style={styles.priceSection}>
          <View style={styles.priceContainer}>
            <TextElement fontSize="4xl" fontWeight="700" style={{color: '#FFFFFF'}}>
              ${currentSelectedPlan?.unit_price.amount || '50'}
            </TextElement>
            <TextElement fontSize="lg" style={{color: '#BCBCBC'}}>
              /month
            </TextElement>
          </View>
        </View>

        <TextElement fontSize="sm" fontWeight="600" style={[styles.featuresTitle, {color: '#FFFFFF'}]}>
          Everything in Free, plus:
        </TextElement>

        <View style={styles.featuresList}>
          {[
            'Generate App build',
            '1 Live App on App Store and Play Store',
            'Push Notifications',
            'Analytics',
            'Full Access to Advanced Coding Model',
            '15-20x higher rate limits',
          ].map((feature, index) => (
            <View key={index} style={styles.featureItem}>
              <MaterialCommunityIcons name="check" size={16} color={theme.SUCCESS} style={styles.checkIcon} />
              <TextElement fontSize="sm" style={[styles.featureText, {color: '#BCBCBC'}]}>
                {feature}
              </TextElement>
            </View>
          ))}
        </View>

        {/* Dropdown for plan selection */}
        <View style={styles.planDropdownContainer}>
          <Pressable style={styles.planDropdown} onPress={() => setDropdownOpen(!dropdownOpen)}>
            <TextElement fontSize="sm" fontWeight="600" style={{color: '#BCBCBC'}}>
              {currentSelectedPlan?.custom_data?.credits || '5000'} monthly credits
            </TextElement>
            <View style={[styles.chevronIcon, dropdownOpen && styles.chevronIconRotated]}>
              <MaterialCommunityIcons name="chevron-down" size={16} color="#BCBCBC" />
            </View>
          </Pressable>

          {/* Dropdown Options */}
          {dropdownOpen && (
            <Pressable style={styles.dropdownOptions} onPress={e => e.stopPropagation()}>
              {monthlyPlans.map((plan, index) => (
                <Pressable
                  key={plan.id}
                  style={[
                    styles.dropdownOption,
                    currentSelectedPlan?.id === plan.id && styles.dropdownOptionSelected,
                    index === monthlyPlans.length - 1 && styles.dropdownOptionLast,
                  ]}
                  onPress={() => handlePlanSelect(plan)}>
                  <View style={styles.dropdownOptionContent}>
                    <TextElement fontSize="sm" fontWeight="500" style={{color: '#FFFFFF'}}>
                      {plan.custom_data?.credits || plan.custom_data?.tokens || '0'} monthly credits
                    </TextElement>
                    <TextElement fontSize="xs" style={{color: '#BCBCBC', marginTop: 2}}>
                      ${plan.unit_price.amount}/month
                    </TextElement>
                  </View>
                </Pressable>
              ))}
            </Pressable>
          )}
        </View>

        <TextElement fontSize="sm" style={[styles.planSubtitle, {color: '#BCBCBC'}]}>
          For more projects and usage
        </TextElement>

        <View style={styles.buttonContainer}>
          <Button
            color="CTA"
            size="MEDIUM"
            onPress={() => currentSelectedPlan && handleUpgrade(currentSelectedPlan)}
            containerStyles={styles.selectButton}>
            Get Started
          </Button>
        </View>
      </View>
    );
  };

  // Render Enterprise Plan Card (hardcoded)
  const renderEnterprisePlanCard = () => (
    <View style={[styles.planCard, styles.enterprisePlanCard]}>
      <TextElement fontSize="xl" fontWeight="600" style={[styles.planTitle, {color: '#FFFFFF'}]}>
        Enterprise
      </TextElement>

      <View style={styles.priceSection}>
        <TextElement fontSize="4xl" fontWeight="700" style={{color: '#FFFFFF'}}>
          Custom
        </TextElement>
      </View>

      <TextElement fontSize="sm" fontWeight="600" style={[styles.featuresTitle, {color: '#FFFFFF'}]}>
        Everything in Pro, plus:
      </TextElement>

      <View style={styles.featuresList}>
        {[
          'Custom Agents',
          'Human experts to lead your project',
          'Tile SDK (Code export and Developer tools)',
          'Premium Support',
          'No rate limits',
        ].map((feature, index) => (
          <View key={index} style={styles.featureItem}>
            <MaterialCommunityIcons name="check" size={16} color={theme.SUCCESS} style={styles.checkIcon} />
            <TextElement fontSize="sm" style={[styles.featureText, {color: '#BCBCBC'}]}>
              {feature}
            </TextElement>
          </View>
        ))}
      </View>

      <View style={styles.creditsContainer}>
        <TextElement fontSize="sm" fontWeight="600" style={[styles.creditsText, {color: '#BCBCBC'}]}>
          Unlimited credits
        </TextElement>
      </View>

      <TextElement fontSize="sm" style={[styles.planSubtitle, {color: '#BCBCBC'}]}>
        For collaborating with others
      </TextElement>

      <View style={styles.buttonContainer}>
        <Button
          color="SECONDARY"
          size="MEDIUM"
          onPress={() => {
            /* Handle enterprise contact */
          }}
          containerStyles={[styles.selectButton, styles.enterpriseButton]}>
          <View style={styles.enterpriseButtonContent}>
            <MaterialCommunityIcons name="calendar" size={18} color={theme.DEFAULT_COLOR} style={{marginRight: 8}} />
            <TextElement fontSize="sm" fontWeight="600" style={{color: '#FFFFFF'}}>
              Book a Call
            </TextElement>
          </View>
        </Button>
      </View>
    </View>
  );

  const renderContent = () => (
    <View style={styles.container}>
      {/* Header */}
      <View style={styles.header}>
        <Pressable onPress={onClose} style={styles.closeButton}>
          <MaterialCommunityIcons name="close" size={24} color="#FFFFFF" />
        </Pressable>
        <TextElement fontSize="3xl" fontWeight="600" style={[styles.title, {color: '#FFFFFF'}]}>
          Pricing
        </TextElement>
        <TextElement fontSize="lg" style={[styles.subtitle, {color: '#BCBCBC'}]}>
          Start for free. Upgrade to get the capacity that exactly matches your team's needs.
        </TextElement>
      </View>

      {/* Plans Container */}
      <Pressable style={styles.plansContainer} onPress={() => setDropdownOpen(false)}>
        {loading ? (
          <View style={styles.loadingContainer}>
            <ActivityIndicator size="large" color={theme.CTA} />
            <TextElement fontSize="lg" style={{marginTop: 16, color: '#BCBCBC'}}>
              Loading plans...
            </TextElement>
          </View>
        ) : error ? (
          <View style={styles.errorContainer}>
            <MaterialCommunityIcons name="alert-circle" size={48} color={theme.ERROR} />
            <TextElement fontSize="lg" style={{marginTop: 16, textAlign: 'center', color: '#FF6B6B'}}>
              {error}
            </TextElement>
            <Button color="CTA" size="MEDIUM" onPress={loadProducts} containerStyles={{marginTop: 16}}>
              Try Again
            </Button>
          </View>
        ) : (
          <View style={styles.plansGrid}>
            {renderFreePlanCard()}
            {renderProPlanCard()}
            {renderEnterprisePlanCard()}
          </View>
        )}
      </Pressable>
    </View>
  );

  return (
    <Modal visible={visible} transparent={true} animationType="fade" onRequestClose={onClose}>
      <Pressable style={styles.modalBackground} onPress={onClose}>
        <Pressable style={styles.modalContainer} onPress={e => e.stopPropagation()}>
          {renderContent()}
        </Pressable>
      </Pressable>
    </Modal>
  );
};

const styles = StyleSheet.create({
  modalBackground: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.6)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  modalContainer: {
    justifyContent: 'center',
    alignItems: 'center',
  },
  container: {
    width: 1024,
    maxWidth: '95vw',
    maxHeight: '95vh',
    backgroundColor: '#0F1114',
    borderRadius: 20,
    padding: 0,
    overflow: 'hidden',
    shadowColor: theme.SECONDARY_COLOR,
    shadowOffset: {width: 0, height: 20},
    shadowOpacity: 0.15,
    shadowRadius: 40,
    elevation: 20,
  },
  header: {
    padding: 40,
    paddingBottom: 32,
    alignItems: 'center',
  },
  closeButton: {
    position: 'absolute',
    top: 20,
    right: 20,
    padding: 12,
    zIndex: 1,
    borderRadius: 8,
    backgroundColor: '#2A2D31',
  },
  title: {
    marginBottom: 12,
    textAlign: 'center',
  },
  subtitle: {
    textAlign: 'center',
    maxWidth: 600,
    lineHeight: 24,
  },
  plansContainer: {
    flex: 1,
    paddingHorizontal: 16,
    paddingBottom: 32,
  },
  plansGrid: {
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'flex-start',
    gap: 24,
    paddingHorizontal: 16,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    minHeight: 300,
  },
  errorContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    minHeight: 300,
    paddingHorizontal: 40,
  },
  planCard: {
    width: 317,
    backgroundColor: '#1A1D20',
    borderRadius: 12,
    borderWidth: 1,
    borderColor: '#5C5C5C',
    padding: 28,
    position: 'relative',
    minHeight: 588,
    shadowColor: theme.SECONDARY_COLOR,
    shadowOffset: {width: 0, height: 4},
    shadowOpacity: 0.08,
    shadowRadius: 12,
    elevation: 4,
    display: 'flex',
    flexDirection: 'column',
  },
  freePlanCard: {
    // Additional styles for free plan if needed
  },
  proPlanCard: {
    borderColor: theme.CTA,
    borderWidth: 3,
  },
  enterprisePlanCard: {
    // Additional styles for enterprise plan if needed
  },
  planTitle: {
    marginBottom: 20,
  },
  planSubtitle: {
    marginTop: 8,
    marginBottom: 16,
  },
  popularBadge: {
    position: 'absolute',
    top: -1,
    left: 0,
    right: 0,
    backgroundColor: '#2B2A47',
    paddingVertical: 8,
    paddingHorizontal: 16,
    borderTopLeftRadius: 12,
    borderTopRightRadius: 12,
    alignItems: 'center',
  },
  creditsContainer: {
    backgroundColor: '#0F1114',
    borderRadius: 6,
    paddingVertical: 10,
    paddingHorizontal: 13,
    marginVertical: 16,
  },
  planDropdownContainer: {
    marginVertical: 16,
    position: 'relative',
    zIndex: 1000,
  },
  planDropdown: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    backgroundColor: '#1A1D20',
    borderWidth: 1,
    borderColor: '#5C5C5C',
    borderRadius: 8,
    paddingVertical: 12,
    paddingHorizontal: 16,
    transition: 'border-color 0.2s ease',
  },
  dropdownOptions: {
    position: 'absolute',
    bottom: '100%',
    left: 0,
    right: 0,
    backgroundColor: '#1A1D20',
    borderWidth: 1,
    borderColor: '#5C5C5C',
    borderRadius: 8,
    marginBottom: 8,
    zIndex: 9999,
    shadowColor: '#000000',
    shadowOffset: {width: 0, height: -4},
    shadowOpacity: 0.25,
    shadowRadius: 8,
    elevation: 15,
    maxHeight: 200,
    overflow: 'hidden',
  },
  dropdownOption: {
    paddingVertical: 14,
    paddingHorizontal: 16,
    borderBottomWidth: 1,
    borderBottomColor: '#3A3D41',
    transition: 'background-color 0.2s ease',
  },
  dropdownOptionSelected: {
    backgroundColor: '#2B2F33',
    borderLeftWidth: 3,
    borderLeftColor: theme.CTA,
  },
  dropdownOptionContent: {
    flex: 1,
  },
  dropdownOptionLast: {
    borderBottomWidth: 0,
  },
  chevronIcon: {
    transition: 'transform 0.2s ease',
  },
  chevronIconRotated: {
    transform: [{rotate: '180deg'}],
  },
  priceSection: {
    marginBottom: 24,
    alignItems: 'flex-start',
  },
  priceContainer: {
    flexDirection: 'row',
    alignItems: 'baseline',
    marginBottom: 8,
  },
  creditsText: {
    textAlign: 'left',
  },
  featuresTitle: {
    marginBottom: 12,
  },
  featuresList: {
    marginBottom: 16,
  },
  featureItem: {
    flexDirection: 'row',
    alignItems: 'center',
    marginVertical: 6,
  },
  checkIcon: {
    marginRight: 12,
  },
  featureText: {
    flex: 1,
    lineHeight: 20,
  },
  buttonContainer: {
    marginTop: 'auto',
    paddingTop: 16,
  },
  selectButton: {
    width: '100%',
  },
  enterpriseButton: {
    backgroundColor: 'linear-gradient(135deg, #114DAD 0%, #161616 100%)',
    borderWidth: 1,
    borderColor: theme.DEFAULT_COLOR,
  },
  enterpriseButtonContent: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
  },
});

export default PricingModal;
